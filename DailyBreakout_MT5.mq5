//+------------------------------------------------------------------+
//|                                           DailyBreakout_MT5.mq5 |
//|                                    Daily Breakout Master Bot MT5 |
//|                          BY BRIAN ALVIN BAGOROGOZA               |
//|                          INNOVATIONX INTERNATIONAL              |
//+------------------------------------------------------------------+
#property copyright "<PERSON> - InnovationX International"
#property version   "1.00"
#property description "Daily Breakout Master Strategy - Professional Trading Bot (MT5)"
#property strict

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input Parameters
input group "=== LOT SIZE & RISK ==="
input ENUM_LOT_TYPE LotSizeType = LOT_RISK_BASED; // Lot Size Type
input double ManualLotSize = 0.01; // Manual Lot Size
input double RiskPercentPerTrade = 1.0; // Risk % Per Trade
input double MaxRiskPerWeek = 5.0; // Max Risk % Per Week

input group "=== TRADE SETTINGS ==="
input int MaxDailyTrades = 1; // Max Trades Per Day (Each Direction)
input double MaxSpread = 3.0; // Max Spread (Pips)
input int Slippage = 2; // Max Slippage (Pips)
input int MagicNumber = 12345; // Magic Number
input string TradeComment = "DailyBreakout"; // Trade Comment

input group "=== TAKE PROFIT SETTINGS ==="
input ENUM_TP_TYPE TakeProfitType = TP_FIXED_RR; // Take Profit Type
input double FixedRiskReward = 2.0; // Fixed Risk:Reward Ratio
input double ATRMultiplier = 2.0; // ATR Multiplier for TP
input int ATRPeriod = 14; // ATR Period
input double CustomTPPips = 50.0; // Custom TP (Pips)

input group "=== PROTECTION & FILTERS ==="
input bool EnableEquityGuard = true; // Enable Equity Guard
input double EquityGuardPercent = 10.0; // Stop if Equity Drops Below %
input double MaxDrawdownPercent = 15.0; // Max Drawdown %
input bool EnableGapFilter = true; // Enable Gap Filter
input double MaxGapPips = 20.0; // Max Gap Size (Pips)
input bool EnableNewsFilter = false; // Enable News Filter
input int NewsFilterMinutes = 30; // News Filter Minutes

input group "=== TIME & SESSION FILTERS ==="
input bool EnableMondayTrading = true; // Trade on Monday
input bool EnableTuesdayTrading = true; // Trade on Tuesday
input bool EnableWednesdayTrading = true; // Trade on Wednesday
input bool EnableThursdayTrading = true; // Trade on Thursday
input bool EnableFridayTrading = true; // Trade on Friday
input int TriggerHour = 23; // Daily Candle Close Hour
input int TriggerMinute = 0; // Daily Candle Close Minute
input int SessionStartHour = 8;
input int SessionEndHour = 17;

input group "=== ADVANCED FEATURES ==="
input ENUM_ENTRY_MODE EntryMode = ENTRY_BODY; // Entry: body or wick
input ENUM_SL_MODE StopLossMode = SL_WICK;    // SL: wick, half, percent, points
input double SLPercent = 50.0;                // For SL_PERCENT: % between wick high/low
input double SLPoints = 0;                    // For SL_POINTS: points from entry

//--- Enums
enum ENUM_LOT_TYPE {
   LOT_MANUAL,      // Manual Lot Size
   LOT_RISK_BASED   // Risk % Based
};

enum ENUM_TP_TYPE {
   TP_FIXED_RR,     // Fixed Risk:Reward
   TP_ATR_BASED,    // ATR Based
   TP_CUSTOM_PIPS   // Custom Pips
};

enum ENUM_ENTRY_MODE {
   ENTRY_BODY, // Use body high/low for entry
   ENTRY_WICK  // Use wick high/low for entry
};
enum ENUM_SL_MODE {
   SL_WICK,    // Use wick high/low for SL
   SL_HALF,    // Use midpoint between wick high/low
   SL_PERCENT, // Use % between wick high/low
   SL_POINTS   // Use points from entry
};

//--- Global Variables
double g_DailyHigh, g_DailyLow, g_DailyOpen, g_DailyClose;
double g_BodyHigh, g_BodyLow;
double g_BuyStopPrice, g_SellStopPrice;
double g_BuyStopLoss, g_SellStopLoss;
double g_BuyTakeProfit, g_SellTakeProfit;
bool g_NewDayProcessed = false;
bool g_BuyTradeToday = false;
bool g_SellTradeToday = false;

// Dashboard Variables
double g_StartingBalance;
double g_DailyStartBalance;
double g_WeeklyStartBalance;
double g_MaxDailyLoss = 0;
double g_MaxDailyLossPercent = 0;
int g_TotalTrades = 0;
int g_WinningTrades = 0;
int g_DailyTrades = 0;
int g_WeeklyTrades = 0;
double g_DailyPL = 0;
double g_WeeklyPL = 0;
datetime g_LastDayCheck = 0;
datetime g_LastWeekCheck = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetDeviationInPoints(Slippage);
   
   g_StartingBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_DailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_WeeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_LastDayCheck = TimeCurrent();
   g_LastWeekCheck = TimeCurrent();
   
   Print("=== DAILY BREAKOUT MASTER BOT MT5 ===");
   Print("Developed by: Brian Alvin Bagorogoza");
   Print("InnovationX International");
   Print("Contact: +49 1521 6294394");
   Print("Bot Initialized Successfully!");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   // Clean up objects
   ObjectsDeleteAll(0, "DB_");
   Print("Daily Breakout Master Bot MT5 - InnovationX International - Deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
   // Check for new day at trigger time
   CheckNewDay();
   
   // Update dashboard
   if(ShowDashboard) UpdateDashboard();
   
   // Check for trade triggers
   CheckTradeSignals();
   
   // Manage existing trades
   ManageExistingTrades();
   
   // Update daily/weekly statistics
   UpdateStatistics();
}

//+------------------------------------------------------------------+
//| Check for new day and process daily levels                      |
//+------------------------------------------------------------------+
void CheckNewDay() {
   datetime currentTime = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(currentTime, dt);
   
   // Check if it's trigger time and we haven't processed today
   if(dt.hour == TriggerHour && dt.min == TriggerMinute) {
      MqlDateTime lastDt;
      TimeToStruct(g_LastDayCheck, lastDt);
      
      if(!g_NewDayProcessed || dt.day != lastDt.day) {
         ProcessNewDay();
         g_NewDayProcessed = true;
         g_LastDayCheck = currentTime;
      }
   } else {
      g_NewDayProcessed = false;
   }
}

//+------------------------------------------------------------------+
//| Process new day - calculate levels and set pending orders       |
//+------------------------------------------------------------------+
void ProcessNewDay() {
   // Get previous day's OHLC
   double high[], low[], open[], close[];
   
   if(CopyHigh(Symbol(), PERIOD_D1, 1, 1, high) <= 0 ||
      CopyLow(Symbol(), PERIOD_D1, 1, 1, low) <= 0 ||
      CopyOpen(Symbol(), PERIOD_D1, 1, 1, open) <= 0 ||
      CopyClose(Symbol(), PERIOD_D1, 1, 1, close) <= 0) {
      Print("Error copying daily data");
      return;
   }
   
   g_DailyHigh = high[0];
   g_DailyLow = low[0];
   g_DailyOpen = open[0];
   g_DailyClose = close[0];
   
   // Calculate body high and low
   g_BodyHigh = MathMax(g_DailyOpen, g_DailyClose);
   g_BodyLow = MathMin(g_DailyOpen, g_DailyClose);

   // Entry levels
   if (EntryMode == ENTRY_BODY) {
      g_BuyStopPrice = g_BodyHigh;
      g_SellStopPrice = g_BodyLow;
   } else if (EntryMode == ENTRY_WICK) {
      g_BuyStopPrice = g_DailyHigh;
      g_SellStopPrice = g_DailyLow;
   }

   // Stop loss logic
   if (StopLossMode == SL_WICK) {
      g_BuyStopLoss = g_DailyLow;
      g_SellStopLoss = g_DailyHigh;
   } else if (StopLossMode == SL_HALF) {
      double mid = (g_DailyHigh + g_DailyLow) / 2.0;
      g_BuyStopLoss = mid;
      g_SellStopLoss = mid;
   } else if (StopLossMode == SL_PERCENT) {
      double range = g_DailyHigh - g_DailyLow;
      g_BuyStopLoss = g_DailyLow + (range * (SLPercent / 100.0));
      g_SellStopLoss = g_DailyHigh - (range * (SLPercent / 100.0));
   } else if (StopLossMode == SL_POINTS && SLPoints > 0) {
      double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
      g_BuyStopLoss = g_BuyStopPrice - (SLPoints * point);
      g_SellStopLoss = g_SellStopPrice + (SLPoints * point);
   }
   
   // Calculate take profits
   CalculateTakeProfits();
   
   // Reset daily trade flags
   g_BuyTradeToday = false;
   g_SellTradeToday = false;
   g_DailyTrades = 0;
   g_DailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_DailyPL = 0;
   
   // Draw zones if enabled
   if(DrawZones) DrawDailyZones();
   
   Print("New day processed. Buy Stop: ", g_BuyStopPrice, " Sell Stop: ", g_SellStopPrice);
}

//+------------------------------------------------------------------+
//| Calculate Take Profit levels based on settings                  |
//+------------------------------------------------------------------+
void CalculateTakeProfits() {
   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double buyRisk = (g_BuyStopPrice - g_BuyStopLoss) / point;
   double sellRisk = (g_SellStopLoss - g_SellStopPrice) / point;

   switch(TakeProfitType) {
      case TP_FIXED_RR:
         g_BuyTakeProfit = g_BuyStopPrice + (buyRisk * FixedRiskReward * point);
         g_SellTakeProfit = g_SellStopPrice - (sellRisk * FixedRiskReward * point);
         break;

      case TP_ATR_BASED:
         int atrHandle = iATR(Symbol(), PERIOD_D1, ATRPeriod);
         double atrBuffer[];
         if(CopyBuffer(atrHandle, 0, 1, 1, atrBuffer) > 0) {
            double atr = atrBuffer[0];
            g_BuyTakeProfit = g_BuyStopPrice + (atr * ATRMultiplier);
            g_SellTakeProfit = g_SellStopPrice - (atr * ATRMultiplier);
         }
         break;

      case TP_CUSTOM_PIPS:
         double pipSize = point;
         if(SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
            pipSize *= 10;
         g_BuyTakeProfit = g_BuyStopPrice + (CustomTPPips * pipSize);
         g_SellTakeProfit = g_SellStopPrice - (CustomTPPips * pipSize);
         break;
   }
}

//+------------------------------------------------------------------+
//| Check for trade signals and execute trades                      |
//+------------------------------------------------------------------+
void CheckTradeSignals() {
   if(!IsTradingAllowed()) return;

   double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
   double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double spread = (ask - bid) / SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   // Check spread filter
   if(spread > MaxSpread) return;

   // Check gap filter
   if(EnableGapFilter && CheckForGap()) return;

   // Check buy signal
   if(!g_BuyTradeToday && g_DailyTrades < MaxDailyTrades) {
      if(ask >= g_BuyStopPrice) {
         double rates[];
         if(CopyClose(Symbol(), PERIOD_CURRENT, 0, 1, rates) > 0) {
            if(!EnableBreakoutConfirmation || rates[0] > g_BuyStopPrice) {
               ExecuteBuyTrade();
            }
         }
      }
   }

   // Check sell signal
   if(!g_SellTradeToday && g_DailyTrades < MaxDailyTrades) {
      if(bid <= g_SellStopPrice) {
         double rates[];
         if(CopyClose(Symbol(), PERIOD_CURRENT, 0, 1, rates) > 0) {
            if(!EnableBreakoutConfirmation || rates[0] < g_SellStopPrice) {
               ExecuteSellTrade();
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Execute Buy Trade                                                |
//+------------------------------------------------------------------+
void ExecuteBuyTrade() {
   double lotSize = CalculateLotSize(g_BuyStopPrice - g_BuyStopLoss);

   if(lotSize > 0) {
      if(trade.Buy(lotSize, Symbol(), 0, g_BuyStopLoss, g_BuyTakeProfit, TradeComment + " Buy")) {
         g_BuyTradeToday = true;
         g_DailyTrades++;
         g_TotalTrades++;
         Print("Buy trade executed. Ticket: ", trade.ResultOrder());
      }
   }
}

//+------------------------------------------------------------------+
//| Execute Sell Trade                                               |
//+------------------------------------------------------------------+
void ExecuteSellTrade() {
   double lotSize = CalculateLotSize(g_SellStopLoss - g_SellStopPrice);

   if(lotSize > 0) {
      if(trade.Sell(lotSize, Symbol(), 0, g_SellStopLoss, g_SellTakeProfit, TradeComment + " Sell")) {
         g_SellTradeToday = true;
         g_DailyTrades++;
         g_TotalTrades++;
         Print("Sell trade executed. Ticket: ", trade.ResultOrder());
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double riskPips) {
   double lotSize = 0;

   switch(LotSizeType) {
      case LOT_MANUAL:
         lotSize = ManualLotSize;
         break;

      case LOT_RISK_BASED:
         double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercentPerTrade / 100.0;
         double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
         double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
         double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

         double pipValue = tickValue * (point / tickSize);
         lotSize = riskAmount / (riskPips * pipValue);
         break;
   }

   // Normalize lot size
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, NormalizeDouble(lotSize / lotStep, 0) * lotStep));

   return lotSize;
}

//+------------------------------------------------------------------+
//| Manage existing trades (trailing stop, partial TP, etc.)       |
//+------------------------------------------------------------------+
void ManageExistingTrades() {
   for(int i = PositionsTotal() - 1; i >= 0; i--) {
      if(position.SelectByIndex(i) && position.Magic() == MagicNumber) {
         if(EnableTrailingStop) {
            ApplyTrailingStop();
         }

         if(EnablePartialTP) {
            CheckPartialTakeProfit();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Apply trailing stop to profitable trades                        |
//+------------------------------------------------------------------+
void ApplyTrailingStop() {
   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double pipSize = point;
   if(SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
      pipSize *= 10;

   double trailDistance = TrailingStopPips * pipSize;

   if(position.PositionType() == POSITION_TYPE_BUY) {
      double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      double newSL = bid - trailDistance;
      if(newSL > position.StopLoss() && newSL < bid) {
         trade.PositionModify(position.Ticket(), newSL, position.TakeProfit());
      }
   } else if(position.PositionType() == POSITION_TYPE_SELL) {
      double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      double newSL = ask + trailDistance;
      if(newSL < position.StopLoss() && newSL > ask) {
         trade.PositionModify(position.Ticket(), newSL, position.TakeProfit());
      }
   }
}

//+------------------------------------------------------------------+
//| Check and execute partial take profit                           |
//+------------------------------------------------------------------+
void CheckPartialTakeProfit() {
   double profit = position.Profit();
   double risk = MathAbs(position.PriceOpen() - position.StopLoss()) * position.Volume() *
                 SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);

   if(profit >= risk) { // At 1:1 RR
      double partialLots = position.Volume() * PartialTPPercent / 100.0;
      if(partialLots >= SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN)) {
         trade.PositionClosePartial(position.Ticket(), partialLots);
      }
   }
}

//+------------------------------------------------------------------+
//| Check if trading is allowed based on filters                    |
//+------------------------------------------------------------------+
bool IsTradingAllowed() {
   // Check equity guard
   if(EnableEquityGuard) {
      double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
      double equityThreshold = g_StartingBalance * (100 - EquityGuardPercent) / 100.0;
      if(currentEquity < equityThreshold) return false;
   }

   // Check max drawdown
   double drawdown = (g_StartingBalance - AccountInfoDouble(ACCOUNT_EQUITY)) / g_StartingBalance * 100.0;
   if(drawdown > MaxDrawdownPercent) return false;

   // Check day of week
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   switch(dt.day_of_week) {
      case 1: if(!EnableMondayTrading) return false; break;
      case 2: if(!EnableTuesdayTrading) return false; break;
      case 3: if(!EnableWednesdayTrading) return false; break;
      case 4: if(!EnableThursdayTrading) return false; break;
      case 5: if(!EnableFridayTrading) return false; break;
   }

   // Check if within session hours
   if (!IsWithinSession()) return false;

   // Check weekly risk limit
   double weeklyRisk = MathAbs(g_WeeklyPL) / g_WeeklyStartBalance * 100.0;
   if(weeklyRisk > MaxRiskPerWeek) return false;

   return true;
}

bool IsWithinSession() {
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   return (dt.hour >= SessionStartHour && dt.hour < SessionEndHour);
}

//+------------------------------------------------------------------+
//| Check for gap conditions                                         |
//+------------------------------------------------------------------+
bool CheckForGap() {
   double open[], close[];
   if(CopyOpen(Symbol(), PERIOD_CURRENT, 0, 1, open) <= 0 ||
      CopyClose(Symbol(), PERIOD_CURRENT, 1, 1, close) <= 0) {
      return false;
   }

   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double pipSize = point;
   if(SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
      pipSize *= 10;

   double gapSize = MathAbs(open[0] - close[0]) / pipSize;
   return (gapSize > MaxGapPips);
}

//+------------------------------------------------------------------+
//| Draw daily zones on chart                                        |
//+------------------------------------------------------------------+
void DrawDailyZones() {
   // Delete existing zones
   ObjectDelete(0, "DB_BuyZone");
   ObjectDelete(0, "DB_SellZone");
   ObjectDelete(0, "DB_WickRange");

   datetime startTime = TimeCurrent();
   datetime endTime = startTime + 86400; // 24 hours

   // Draw wick range
   ObjectCreate(0, "DB_WickRange", OBJ_RECTANGLE, 0, startTime, g_DailyHigh, endTime, g_DailyLow);
   ObjectSetInteger(0, "DB_WickRange", OBJPROP_COLOR, clrGray);
   ObjectSetInteger(0, "DB_WickRange", OBJPROP_STYLE, STYLE_DOT);
   ObjectSetInteger(0, "DB_WickRange", OBJPROP_BACK, true);

   // Draw buy zone (body high)
   ObjectCreate(0, "DB_BuyZone", OBJ_HLINE, 0, 0, g_BodyHigh);
   ObjectSetInteger(0, "DB_BuyZone", OBJPROP_COLOR, BuyZoneColor);
   ObjectSetInteger(0, "DB_BuyZone", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, "DB_BuyZone", OBJPROP_WIDTH, 2);

   // Draw sell zone (body low)
   ObjectCreate(0, "DB_SellZone", OBJ_HLINE, 0, 0, g_BodyLow);
   ObjectSetInteger(0, "DB_SellZone", OBJPROP_COLOR, SellZoneColor);
   ObjectSetInteger(0, "DB_SellZone", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, "DB_SellZone", OBJPROP_WIDTH, 2);
}

//+------------------------------------------------------------------+
//| Update statistics and P&L tracking                              |
//+------------------------------------------------------------------+
void UpdateStatistics() {
   MqlDateTime currentDt, lastDayDt, lastWeekDt;
   TimeToStruct(TimeCurrent(), currentDt);
   TimeToStruct(g_LastDayCheck, lastDayDt);
   TimeToStruct(g_LastWeekCheck, lastWeekDt);

   // Check for new day
   if(currentDt.day != lastDayDt.day) {
      g_DailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
      g_DailyPL = 0;
      g_DailyTrades = 0;
      g_LastDayCheck = TimeCurrent();
   }

   // Check for new week
   if(currentDt.day_of_week == 1 && lastWeekDt.day_of_week != 1) {
      g_WeeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
      g_WeeklyPL = 0;
      g_WeeklyTrades = 0;
      g_LastWeekCheck = TimeCurrent();
   }

   // Calculate current P&L
   g_DailyPL = AccountInfoDouble(ACCOUNT_BALANCE) - g_DailyStartBalance;
   g_WeeklyPL = AccountInfoDouble(ACCOUNT_BALANCE) - g_WeeklyStartBalance;

   // Update max daily loss
   if(g_DailyPL < g_MaxDailyLoss) {
      g_MaxDailyLoss = g_DailyPL;
      g_MaxDailyLossPercent = g_MaxDailyLoss / g_DailyStartBalance * 100.0;
   }

   // Count winning trades
   g_WinningTrades = 0;
   for(int i = 0; i < HistoryDealsTotal(); i++) {
      ulong ticket = HistoryDealGetTicket(i);
      if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == MagicNumber) {
         if(HistoryDealGetDouble(ticket, DEAL_PROFIT) > 0) g_WinningTrades++;
      }
   }
}

//+------------------------------------------------------------------+
//| Update and display dashboard                                     |
//+------------------------------------------------------------------+
void UpdateDashboard() {
   int yPos = 30;
   int xPos = 20;
   int lineHeight = 18;

   // Header with branding
   CreateLabel("DB_Header", "DAILY BREAKOUT MASTER - InnovationX International",
               xPos, yPos, clrGold);
   yPos += lineHeight;

   CreateLabel("DB_Developer", "By Brian Alvin Bagorogoza | +49 1521 6294394",
               xPos, yPos, clrSilver);
   yPos += lineHeight + 5;

   // Account Balance
   CreateLabel("DB_Balance", "Account Balance: $" + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2),
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Daily P&L
   color dailyColor = (g_DailyPL >= 0) ? clrLimeGreen : clrRed;
   CreateLabel("DB_DailyPL", "Daily P&L: $" + DoubleToString(g_DailyPL, 2) +
               " (" + DoubleToString(g_DailyPL/g_DailyStartBalance*100, 2) + "%)",
               xPos, yPos, dailyColor);
   yPos += lineHeight;

   // Weekly P&L
   color weeklyColor = (g_WeeklyPL >= 0) ? clrLimeGreen : clrRed;
   CreateLabel("DB_WeeklyPL", "Weekly P&L: $" + DoubleToString(g_WeeklyPL, 2) +
               " (" + DoubleToString(g_WeeklyPL/g_WeeklyStartBalance*100, 2) + "%)",
               xPos, yPos, weeklyColor);
   yPos += lineHeight;

   // Max Daily Loss
   CreateLabel("DB_MaxLoss", "Max Daily Loss: $" + DoubleToString(g_MaxDailyLoss, 2) +
               " (" + DoubleToString(g_MaxDailyLossPercent, 2) + "%)",
               xPos, yPos, clrOrange);
   yPos += lineHeight;

   // Risk per Trade
   double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercentPerTrade / 100.0;
   CreateLabel("DB_RiskPerTrade", "Risk per Trade: $" + DoubleToString(riskAmount, 2) +
               " (" + DoubleToString(RiskPercentPerTrade, 1) + "%)",
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Max Weekly Risk
   CreateLabel("DB_WeeklyRisk", "Max Weekly Risk: " + DoubleToString(MaxRiskPerWeek, 1) + "%",
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Win Rate
   double winRate = (g_TotalTrades > 0) ? (double)g_WinningTrades / g_TotalTrades * 100.0 : 0;
   CreateLabel("DB_WinRate", "Win Rate: " + DoubleToString(winRate, 1) + "% (" +
               IntegerToString(g_WinningTrades) + "/" + IntegerToString(g_TotalTrades) + ")",
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Today's Trades
   CreateLabel("DB_DailyTrades", "Today's Trades: " + IntegerToString(g_DailyTrades) +
               "/" + IntegerToString(MaxDailyTrades),
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Current Levels
   CreateLabel("DB_BuyLevel", "Buy Stop: " + DoubleToString(g_BuyStopPrice, (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS)),
               xPos, yPos, BuyZoneColor);
   yPos += lineHeight;

   CreateLabel("DB_SellLevel", "Sell Stop: " + DoubleToString(g_SellStopPrice, (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS)),
               xPos, yPos, SellZoneColor);
}

//+------------------------------------------------------------------+
//| Create or update text label                                     |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr) {
   if(ObjectFind(0, name) == -1) {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   }
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
}
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
}
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
}
